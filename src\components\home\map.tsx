import area_image from "@/assets/images/area.svg";
import floor_lamp_icon from "@/assets/images/Floor Lamp.svg";
import hero_image from "@/assets/images/hero.jpg";
import PA_image from "@/assets/images/PA.png";
import pulse_icon from "@/assets/images/Pulse 2.svg";
import trees_image from "@/assets/images/trees.svg";
import water_icon from "@/assets/images/Water.svg";
import { cn } from "@/lib/utils";
import { Trees } from "lucide-react";
import { useState } from "react";

export default function Map() {
  const [activeTab, setActiveTab] = useState(1);

  const forestTabs = [
    {
      key: 1,
      label: "غابات جبلية",
      area: "250 كم2",
      trees: "5000",
      icon: pulse_icon,
    },
    {
      key: 2,
      label: "غابات أودية",
      area: "300 كم2",
      trees: "6000",
      icon: floor_lamp_icon,
    },
    {
      key: 3,
      label: "غابات المانجروف",
      area: "350 كم2",
      trees: "7000",
      icon: water_icon,
    },
  ];

  const selectedForest = forestTabs.find((tab) => tab.key === activeTab);

  return (
    <section
      style={{ backgroundImage: `url(${hero_image})` }}
      className="relative flex h-screen items-center bg-cover bg-center bg-no-repeat"
    >
      <div className="absolute inset-0 bg-gradient-to-b from-[#000]/10 to-[#02302C]/90"></div>
      <div className="relative z-50 container px-4 py-10 text-white sm:px-6 sm:py-16 md:px-8 md:py-20">
        <div className="flex flex-col gap-2 text-center">
          <h2 className="pt-[150px] text-2xl font-[600] sm:text-3xl md:text-4xl lg:text-5xl xl:text-[55px]">
            مستكشف الغابات
          </h2>
          {/* <div className="text-sm font-[400] sm:text-base md:text-lg lg:text-[20px]">
            تحويل إدارة الغابات من خلال التكنولوجيا والاستدامة
          </div> */}
          {/* <Link
            to={"#"}
            className="text-sm font-[400] underline sm:text-base md:text-lg lg:text-[20px]"
          >
            عرض المستكشف الكامل
          </Link> */}
        </div>

        <div className="my-5 flex flex-wrap items-center justify-center gap-2 sm:gap-4 md:gap-6 lg:gap-10">
          {forestTabs.map(({ key, label, icon }) => (
            <div key={key}>
              <div
                className={cn(
                  "flex cursor-pointer items-center justify-center gap-1 rounded-lg px-2 py-2 text-xs font-[500] sm:px-3 sm:text-sm md:px-4 md:text-base",
                  key === activeTab ? "bg-[#035859]" : "bg-[#FFFFFF33]",
                )}
                onClick={() => setActiveTab(key)}
              >
                <img
                  src={icon}
                  alt=""
                  className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6"
                />
                <span className="whitespace-nowrap">{label}</span>
              </div>
            </div>
          ))}
        </div>

        <div className="relative grid grid-cols-4">
          <img
            src={PA_image}
            alt=""
            className="-z-10 col-span-3 m-auto -mt-[6%] h-auto w-[800px] object-contain"
          />

          <div className="flex h-fit w-full max-w-[330px] flex-col gap-3 self-center rounded-md border border-[#FFFFFF36] bg-gradient-to-l from-[#035859] to-[#04595A12] p-3 sm:gap-4 sm:p-4 md:gap-5 md:p-5">
            <div className="rounded-md bg-gradient-to-l from-[#FFFFFF]/30 to-[#FFFFFF00] p-2">
              <div className="flex items-center gap-2 text-sm font-[600] sm:text-base md:text-lg lg:text-[20px]">
                <Trees className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />
                {/* غابة الخالة */}
                {selectedForest?.label}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="flex flex-1 flex-col items-center justify-center gap-1 rounded-md bg-[#FFFFFF1F] p-2 sm:p-3">
                <img
                  src={area_image}
                  alt=""
                  className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6"
                />
                <div className="text-xs font-[500] sm:text-[13px]">
                  {selectedForest?.area}
                </div>
                <div className="text-[10px] font-[400] text-[#FFFFFFB2] sm:text-[12px]">
                  المساحة
                </div>
              </div>
              <div className="flex flex-1 flex-col items-center justify-center gap-1 rounded-md bg-[#FFFFFF1F] p-2 sm:p-3">
                <img
                  src={trees_image}
                  alt=""
                  className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6"
                />
                <div className="text-xs font-[500] sm:text-[13px]">
                  {selectedForest?.trees}
                </div>
                <div className="text-[10px] font-[400] text-[#FFFFFFB2] sm:text-[12px]">
                  عدد الغابات
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
