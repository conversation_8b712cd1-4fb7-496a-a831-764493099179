import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { arLocales } from "./ar-locales";
import { enLocales } from "./en-locales";

const resources = {
  en: {
    translation: enLocales,
  },
  ar: {
    translation: arLocales,
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: localStorage.getItem("gdf-locale") || "en",
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
